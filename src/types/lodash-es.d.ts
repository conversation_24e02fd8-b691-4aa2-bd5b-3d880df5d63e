declare module 'lodash-es' {
  export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait?: number,
    options?: {
      leading?: boolean
      maxWait?: number
      trailing?: boolean
    }
  ): T & { cancel(): void; flush(): ReturnType<T> }

  export function memoize<T extends (...args: any[]) => any>(
    func: T,
    resolver?: (...args: Parameters<T>) => any
  ): T & { cache: Map<any, any> }

  export function throttle<T extends (...args: any[]) => any>(
    func: T,
    wait?: number,
    options?: {
      leading?: boolean
      trailing?: boolean
    }
  ): T & { cancel(): void; flush(): ReturnType<T> }

  export function cloneDeep<T>(value: T): T
  export function isEqual(value: any, other: any): boolean
  export function isEmpty(value: any): boolean
  export function pick<T, K extends keyof T>(object: T, ...props: K[]): Pick<T, K>
  export function omit<T, K extends keyof T>(object: T, ...props: K[]): Omit<T, K>
}