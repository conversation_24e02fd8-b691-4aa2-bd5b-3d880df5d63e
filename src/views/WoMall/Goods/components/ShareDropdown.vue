<template>
  <div
    class="share-dropdown-wrapper"
    v-show="visible"
  >
    <button
      class="share-dropdown__trigger"
      type="button"
      @click.stop="toggleMenu"
      :aria-expanded="show"
      aria-label="打开菜单"
      :class="{ 'share-dropdown__trigger--active': show }"
    >
      <slot>
        <div class="share-dropdown__dots">
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
        </div>
      </slot>
    </button>

    <Transition name="mask-fade">
      <div
        v-if="show"
        class="share-dropdown__mask"
        @click="hidePop"
      />
    </Transition>

    <Transition name="menu-slide">
      <div
        v-if="show"
        class="share-dropdown__menu"
        role="menu"
        aria-label="功能菜单"
      >
        <nav class="share-dropdown__nav" @click.stop>
          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toHomeHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--home" aria-hidden="true" />
            <span class="share-dropdown__text">首页</span>
          </button>

          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toCategoryHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--category" aria-hidden="true" />
            <span class="share-dropdown__text">分类</span>
          </button>

          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toUserHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--user" aria-hidden="true" />
            <span class="share-dropdown__text">我的</span>
          </button>

          <button
            v-if="!isShowShare"
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="shareHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--share" aria-hidden="true" />
            <span class="share-dropdown__text">分享</span>
          </button>
        </nav>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { isHarmonyOS } from 'commonkit'

defineProps({
  visible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['share'])
const router = useRouter()
const show = ref(false)

const isShowShare = computed(() => {
  return isHarmonyOS
})

const hidePop = () => {
  show.value = false
}

const toggleMenu = () => {
  show.value = !show.value
}

const toHomeHandler = () => {
  router.push('/home')
  show.value = false
}

const toCategoryHandler = () => {
  router.push({ path: '/category', query: { _t: new Date().getTime().toString() } })
  show.value = false
}

const toUserHandler = () => {
  router.push('/user')
  show.value = false
}

const shareHandler = (e) => {
  emit('share', e)
  show.value = false
}

onActivated(() => {
  show.value = false
})
</script>

<style lang="less" scoped>
.share-dropdown-wrapper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.share-dropdown__trigger {
  .button-base();
  padding: 0;
  background: transparent;
  border: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.25s ease-out;
  transform-origin: center;

  &:active {
    transform: scale(0.92);
  }

  &--active {
    .share-dropdown__dots {
      background: @bg-color-white;
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 122, 10, 0.1);
      transform: scale(1.02);

      .share-dropdown__dot {
        background: @theme-color;
        transform: scale(1.2);
      }
    }
  }
}

.share-dropdown__dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 3px;
  background: @bg-color-white;
  border-radius: @radius-50;
  width: 35px;
  height: 35px;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.25s ease-out;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.share-dropdown__dot {
  width: 4px;
  height: 4px;
  background: @text-color-primary;
  border-radius: @radius-50;
  transition: all 0.25s ease-out;
}

.share-dropdown__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: -1;
}

.share-dropdown__menu {
  position: absolute;
  top: 60px;
  right: 0;
  z-index: 1;
}

.share-dropdown__nav {
  @arrow-offset: 16px;
  @arrow-size: 10px;
  @menu-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

  position: relative;
  background: @bg-color-white;
  color: @text-color-primary;
  border-radius: @radius-12;
  box-shadow: @menu-shadow;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 140px;

  &::before {
    content: '';
    position: absolute;
    top: -@arrow-size;
    right: @arrow-offset;
    width: 0;
    height: 0;
    border-left: @arrow-size solid transparent;
    border-right: @arrow-size solid transparent;
    border-bottom: @arrow-size solid @bg-color-white;
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.08));
  }
}

.share-dropdown__item {
  .button-base();
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 10px;
  background: transparent;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease-out;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  min-height: 35px;

  &:first-child {
    border-radius: @radius-12 @radius-12 0 0;
  }

  &:last-child {
    border-radius: 0 0 @radius-12 @radius-12;
  }

  &:hover {
    background: linear-gradient(90deg, rgba(255, 122, 10, 0.06) 0%, rgba(255, 122, 10, 0.02) 100%);

    .share-dropdown__icon {
      transform: scale(1.1);
    }

    .share-dropdown__text {
      color: @theme-color;
      font-weight: @font-weight-600;
    }
  }

  &:active {
    background: linear-gradient(90deg, rgba(255, 122, 10, 0.12) 0%, rgba(255, 122, 10, 0.04) 100%);
    transform: scale(0.98);
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 18px;
    right: 18px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, @divider-color-base 20%, @divider-color-base 80%, transparent 100%);
    opacity: 0.6;
  }
}

.share-dropdown__icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-size: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  flex-shrink: 0;
  transition: all 0.2s ease-out;
  border-radius: @radius-6;
  padding: 2px;

  &--home {
    background-image: url(../assets/icon-home.png);
  }

  &--user {
    background-image: url(../assets/icon-my.png);
  }

  &--share {
    background-image: url(../assets/icon-share.png);
  }

  &--category {
    background-image: url(../assets/icon-category.png);
  }
}

.share-dropdown__text {
  .text-body();
  margin-left: 14px;
  flex: 1;
  text-align: left;
  font-size: @font-size-16;
  font-weight: @font-weight-500;
  transition: all 0.2s ease-out;
  letter-spacing: 0.2px;
}

.mask-fade-enter-active,
.mask-fade-leave-active {
  transition: opacity 0.25s ease-out;
}

.mask-fade-enter-from,
.mask-fade-leave-to {
  opacity: 0;
}

.menu-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

.menu-slide-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.menu-slide-enter-from {
  opacity: 0;
  transform: translateY(-12px) scale(0.92);
}

.menu-slide-leave-to {
  opacity: 0;
  transform: translateY(-6px) scale(0.96);
}

.share-dropdown__item {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: @theme-color;
    transform: scaleY(0);
    transition: transform 0.2s ease-out;
    border-radius: 0 2px 2px 0;
  }

  &:hover::before {
    transform: scaleY(1);
  }
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .share-dropdown__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}

</style>
