<template>
  <div class="goods-list-page">
    <header class="page-header">
      <SearchHeader
        v-model="searchKeyword"
        placeholder="搜索商品"
        :redirectToSearch="true"
        redirectUrl="/search"
        @search="handleSearch"
        @clickable="handleSearchClick"
      >
        <template #right-action>
          <button class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <SortFilterBar
        :sort-type="sortType"
        :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions"
        @sort-change="handleSortChange"
        @filter-toggle="toggleFilter"
      />
    </header>

    <main class="goods-content">
      <GoodsListLayout
        :goods-list="goodsList"
        :is-loading="isLoading"
        :loading="loading"
        :finished="finished"
        :is-waterfall="isWaterfallLayout"
        :breakpoints="breakpoints"
        empty-description="本地区无货"
        @load-more="onLoad"
        @item-click="goToDetail"
        @add-cart="addOneCart"
        @update:loading="(val) => loading = val"
      />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart" />

    <FilterPopup
      v-model:show="isPopupShow"
      v-model="filterCriteria"
      :location-text="locationText"
      :category-id="categoryId"
      @switch-address="setSwitchAddressPopupShow"
      @confirm="handleFilterConfirm"
      @reset="handleFilterReset"
    />

    <AddressSwitchPopup
      v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { filter } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@/components/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@/components/FilterTools/FilterPopup.vue'
import FloatingBubble from '@/components/FilterTools/FloatingBubble.vue'
import SortFilterBar from '@/components/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@/components/GoodsCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { skuPageList } from '@/api/index.js'
import { closeToast, showLoadingToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@/store/modules/user.js'
import { getDefaultBreakpoints } from '@/config/responsive.js'

const userStore = useUserStore()
const route = useRoute()

// 使用商品列表组合函数
const {
  goodsList,
  loading,
  finished,
  isLoading,
  pageNo,
  pageSize,
  filterCriteria,
  hasFilterConditions,
  locationText,
  addressInfo,
  resetList,
  processGoodsData,
  applyStockFilter,
  goToDetail,
  goToCart,
  addOneCart,
  handleFilterReset,
  handleAddressChanged
} = useGoodsList()

// 页面特有状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('desc')
const isWaterfallLayout = ref(false)
const categoryId = ref('')
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 瀑布流配置
const breakpoints = ref(getDefaultBreakpoints())

// 页面方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

const handleSearch = () => {
  // 搜索功能在此页面不实现，由重定向处理
}

const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  resetList()
  fetchGoodsList()
}

const handleFilterConfirm = () => {
  resetList()
  fetchGoodsList()
}

// 获取商品列表
const fetchGoodsList = async () => {
  if (pageNo.value === 1) {
    isLoading.value = true
  }

  const brandList = filter(filterCriteria.value.brandsList, 'isSelected').map(item => item.value)

  showLoadingToast()
  const [err, json] = await skuPageList({
    bizCode: getBizCode('GOODS'),
    categoryId: categoryId.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    type: sortType.value,
    sort: sortOrder.value,
    brandList: JSON.stringify(brandList),
    priceFrom: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice * 100) : '',
    priceTo: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice * 100) : '',
    addressInfo: addressInfo.value
  })
  closeToast()

  if (pageNo.value === 1) {
    goodsList.value = []
  }

  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.skuList && json.skuList.length > 0) {
      const processedList = processGoodsData(json.skuList)
      const filteredList = applyStockFilter(processedList)

      if (filteredList.length <= 0 && json.cacheType === '1') {
        pageNo.value++
        fetchGoodsList()
        return
      }

      goodsList.value = goodsList.value.concat(filteredList)

      if (json.cacheType === '1') {
        pageNo.value++
      } else {
        finished.value = true
      }
    } else {
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true
        return
      }
      pageNo.value++
      fetchGoodsList()
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
  }
}

const onLoad = () => {
  fetchGoodsList()
}

onMounted(async () => {
  categoryId.value = route.params.id

  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: 85px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 10px;
  }
}
</style>
