<template>
  <div class="goods-list-container" :style="containerStyle">
    <div class="goods-list-header">
      <img v-if="icon" class="header-icon" :src="icon" alt="icon">
      <div class="header-title">{{ title }}</div>
      <div v-if="desc" class="header-desc">{{ desc }}</div>
      <div v-if="more" class="header-more" @click="onMoreClick">{{ more }}</div>
    </div>
    <div class="goods-scroll-container" v-if="hasValidGoodsList">
      <div class="goods-list">
        <div
          class="goods-item"
          v-for="(item, index) in displayGoodsList"
          :key="item.id || index"
          @click="onGoodsClick(item)"
        >
          <div class="goods-img-container">
            <img
              class="goods-img"
              v-lazy="item.listImageUrl"
              :alt="item.skuList[0]?.name || '商品图片'"
            >
          </div>
          <div class="goods-info">
            <div class="goods-name">{{ item.skuList[0]?.name || '商品名称' }}</div>
            <div
              class="goods-sold"
            >
              {{ getSaleVolumeText(item.skuList[0]?.realSaleVolume) }}
            </div>
          </div>
          <div class="goods-price">
            <span>¥</span>{{ formatPrice(item.skuList[0]?.price) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <div class="empty-text">暂无商品</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'


const props = defineProps({
  goodsList: {
    type: Array,
    default: () => [],
    validator: (value) => Array.isArray(value)
  },
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '商品列表',
    required: true
  },
  desc: {
    type: String,
    default: ''
  },
  more: {
    type: String,
    default: ''
  },
  containerStyle: {
    type: String,
    default: ''
  }
})


const emit = defineEmits(['moreClick', 'goodsClick'])


const hasValidGoodsList = computed(() => {
  return props.goodsList && props.goodsList.length > 0
})

const displayGoodsList = computed(() => {
  return props.goodsList.filter(item =>
    item &&
    item.skuList &&
    item.skuList.length > 0 &&
    item.listImageUrl
  )
})


const formatPrice = (price) => {
  if (typeof price !== 'number') return '0.00'
  return (price / 100).toFixed(2)
}

const getSaleVolumeText = (volume) => {
  return `已售${volume}件`
}


const onMoreClick = () => {
  emit('moreClick')
}

const onGoodsClick = (item) => {
  if (item && item.id && item.skuList?.[0]?.skuId) {
    emit('goodsClick', item)
  }
}
</script>

<style lang="less" scoped>
.goods-list-container {
  margin: 10px;
  padding: 15px 10px;
  border: 1px solid #fff;
  box-shadow: 0px 7px 10px 0px rgba(0, 0, 0, 0.01);
  border-radius: 15px;

  .goods-list-header {
    display: flex;
    align-items: flex-end;
    margin-bottom: 12px;

    .header-icon {
      margin-right: 8px;
      width: 25px;
      height: 25px;
      transform: translateY(1px);
    }

    .header-title {
      margin-right: 8px;
      line-height: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .header-desc {
      line-height: 20px;
      font-size: 12px;
      color: #979797;
      font-weight: 400;
    }

    .header-more {
      position: relative;
      margin-left: auto;
      padding-right: 16px;
      line-height: 20px;
      font-size: 12px;
      color: #979797;
      font-weight: 400;

      &::after {
        content: '';
        position: absolute;
        right: 4px;
        top: 48%;
        transform: translateY(-50%);
        width: 6px;
        height: 10px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='10' viewBox='0 0 6 10' fill='none'%3E%3Cpath d='M1 1L5 5L1 9' stroke='%23999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
      }
    }
  }

  .goods-scroll-container {
    overflow: hidden;
    overflow-x: scroll;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .goods-list {
    display: flex;
    min-width: 100%;

    .goods-item {
      flex: 0 0 calc(33.33% - 8px); // 计算方式：(100% / 3) - ((12px+12px) / 3))
      margin-right: 12px;
      display: flex;
      flex-direction: column;

      &:last-child {
        margin-right: 0;
      }

      .goods-img-container {
        margin-bottom: 8px;
        border-radius: 8px;
        overflow: hidden;
        font-size: 0;
      }

      .goods-img {
        width: 100%;
        aspect-ratio: 1/1;
        object-fit: cover;
      }

      .goods-info {
        height: 46px;
      }

      .goods-name {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 4px;
        line-height: 18px;
        font-size: 14px;
        color: #333;
        font-weight: 400;
      }

      .goods-sold {
        font-size: 12px;
        color: #B1B1B1;
        font-weight: 400;
        transform: scale(0.9);
        transform-origin: left;
      }

      .goods-price {
        font-family: OPPOSans-B;
        font-size: 16px;
        color: #DD2B2B;
        font-weight: 400;

        span {
          margin-right: 2px;
          font-size: 18px;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;

    .empty-text {
      font-size: 14px;
      color: #999;
      font-weight: 400;
    }
  }
}
</style>
