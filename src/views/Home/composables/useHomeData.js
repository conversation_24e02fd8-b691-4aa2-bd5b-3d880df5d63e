import { ref, nextTick } from 'vue'
import { throttle } from 'lodash-es'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList, getPartionList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { closeToast, showLoadingToast } from 'vant'

export function useHomeData() {
  // 通用数据状态
  const headerBannerList = ref([])
  const gridMenuItems = ref([])
  const skeletonStates = ref({
    banner: true,
    gridMenu: true,
    waterfall: true
  })
  
  const moduleDataReady = ref({
    banner: false,
    gridMenu: false,
    waterfall: false
  })

  // 瀑布流数据状态
  const waterfallGoodsList = ref([])
  const waterfallLoading = ref(false)
  const waterfallFinished = ref(false)
  const waterfallCurrentPage = ref(1)
  const waterfallPageSize = ref(10)
  const waterfallButtonCanShow = ref(false)
  const waterfallRenderComplete = ref(false)

  // 通用工具函数
  const channelFilterd = (list) => {
    if (isUnicom) {
      return list.filter(item => item.channelType === '1')
    } else if (isWopay) {
      return list.filter(item => item.channelType === '0')
    } else {
      return list.filter(item => item.channelType === '2')
    }
  }

  const transformGoodsData = (item) => ({
    name: item.name || item.goodName,
    price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
    sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
    goodsId: item.id || item.goodsId,
    image: item.listImageUrl || item.image,
    spec: item.spec || item.goodsSpec,
  })

  // 骨架屏控制
  const hideSkeletonInOrder = async (moduleOrder = ['banner', 'gridMenu']) => {
    for (const module of moduleOrder) {
      if (moduleDataReady.value[module] && skeletonStates.value[module]) {
        skeletonStates.value[module] = false
        await nextTick()
      }
    }
  }

  // 获取Banner数据
  const getHeaderBannerList = async (showPage = 1) => {
    const [err, json] = await getBannerInfo({ 
      bizCode: getBizCode('QUERY'), 
      showPage 
    })
    
    if (!err) {
      const bannerData = channelFilterd(json).map(item => ({
        type: 'image',
        url: item.imgUrl,
        alt: item.bannerChName,
        linkUrl: item.url,
      }))
      headerBannerList.value = bannerData
    }

    moduleDataReady.value.banner = true
    await hideSkeletonInOrder(['banner'])
  }

  // 获取图标菜单数据
  const getIconList = async (showPage = 2) => {
    const [err, json] = await getIconInfo({
      bizCode: getBizCode('QUERY'),
      channel: curChannelBiz.get(),
      showPage
    })

    if (!err && json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData
    } else {
      gridMenuItems.value = []
    }

    moduleDataReady.value.gridMenu = true
    await hideSkeletonInOrder(['banner', 'gridMenu'])
  }

  // 获取商品列表数据
  const getWaterfallList = async (id, sortType = '', isLoadMore = false) => {
    if (waterfallLoading.value || (waterfallFinished.value && isLoadMore)) return
    
    waterfallLoading.value = true
    
    // 只在首次加载且显示骨架屏时显示loading toast
    const isFirstLoad = waterfallGoodsList.value.length === 0
    if (!isLoadMore && isFirstLoad && skeletonStates.value.waterfall) {
      waterfallRenderComplete.value = false
      showLoadingToast()
    }

    const params = {
      type: 'partion',
      id,
      bizCode: getBizCode('GOODS'),
      page_no: waterfallCurrentPage.value,
      page_size: waterfallPageSize.value,
    }
    
    if (sortType) {
      params.price_sort = sortType
    }

    const [err, json] = await getGoodsList(params)
    
    if (!isLoadMore && isFirstLoad && skeletonStates.value.waterfall) {
      closeToast()
    }

    if (!err && json) {
      const newItems = json.map(transformGoodsData)

      if (isLoadMore) {
        waterfallGoodsList.value = [...waterfallGoodsList.value, ...newItems]
        waterfallCurrentPage.value++
      } else {
        // 平滑替换数据，避免页面跳动
        waterfallGoodsList.value = newItems
        waterfallCurrentPage.value = 2
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }

      waterfallFinished.value = json.length === 0
      waterfallButtonCanShow.value = true
    } else {
      waterfallFinished.value = true
      if (!isLoadMore) {
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }
    }

    waterfallLoading.value = false
  }

  // 加载更多商品
  const handleWaterfallLoadMore = throttle((goodsPoolId) => {
    if (!waterfallFinished.value && !waterfallLoading.value) {
      getWaterfallList(goodsPoolId, '', true)
    }
  }, 300)

  // 重置瀑布流状态
  const resetWaterfallState = () => {
    waterfallGoodsList.value = []
    waterfallCurrentPage.value = 1
    waterfallFinished.value = false
    waterfallLoading.value = false
    waterfallButtonCanShow.value = false
    waterfallRenderComplete.value = false
  }

  // 获取分区列表
  const getPartionListData = async (type = 2) => {
    showLoadingToast()
    const [err, json] = await getPartionList({ 
      bizCode: getBizCode('GOODS'), 
      type 
    })
    closeToast()
    
    if (err) {
      return []
    }

    return json ? json.sort((a, b) => b.pos - a.pos) : []
  }

  return {
    // 数据状态
    headerBannerList,
    gridMenuItems,
    skeletonStates,
    moduleDataReady,
    waterfallGoodsList,
    waterfallLoading,
    waterfallFinished,
    waterfallCurrentPage,
    waterfallPageSize,
    waterfallButtonCanShow,
    waterfallRenderComplete,
    
    // 工具函数
    channelFilterd,
    transformGoodsData,
    hideSkeletonInOrder,
    
    // API 方法
    getHeaderBannerList,
    getIconList,
    getWaterfallList,
    handleWaterfallLoadMore,
    resetWaterfallState,
    getPartionListData
  }
}