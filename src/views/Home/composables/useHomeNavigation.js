import { useRouter } from 'vue-router'

export function useHomeNavigation() {
  const router = useRouter()

  // 处理商品点击
  const handleGoodsClick = (goodsInfo) => {
    if (goodsInfo.goodsId) {
      router.push(`/goodsdetail/${goodsInfo.goodsId}`)
    }
  }

  // 处理Banner点击
  const handleBannerClick = ({ item }) => {
    if (item.linkUrl) {
      window.location.href = item.linkUrl
    }
  }

  // 处理网格菜单点击
  const handleGridItemClick = ({ item }) => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  // 处理更多按钮点击
  const handleMoreClick = () => {
    // 可以根据需要实现具体逻辑
    console.log('更多按钮被点击')
  }

  // 处理活动点击
  const handleActivityClick = (item, position = '') => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  return {
    handleGoodsClick,
    handleBannerClick,
    handleGridItemClick,
    handleMoreClick,
    handleActivityClick
  }
}