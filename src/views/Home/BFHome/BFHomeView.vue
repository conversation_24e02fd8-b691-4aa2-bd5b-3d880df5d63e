<template>
  <BaseHomeLayout home-class="bf-home" search-placeholder="搜索商品" :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems" :grid-columns="5" :skeleton-states="skeletonStates" @search="handleSearch"
    @banner-click="handleBannerClick" @grid-item-click="handleGridItemClick" @more-click="handleMoreClick">
    <template #main-content>
      <SectionContainer v-if="skeletonStates.limited || limitedList.length > 0" title="各县销冠">
        <WaterfallSection :waterfall-goods-list="limitedList" :waterfall-loading="limitedLoading"
          :waterfall-finished="limitedFinished" :waterfall-button-can-show="limitedButtonCanShow"
          :waterfall-render-complete="true" :skeleton-states="{ waterfall: skeletonStates.limited }"
          @goods-click="handleGoodsClick" @load-more="handleLimitedLoadMore" />
      </SectionContainer>

      <SectionContainer v-if="skeletonStates.newer || newerList.length > 0" title="新上好物">
        <HorizontalScrollSection card-type="mini" :goods-list="newerList" :skeleton-states="{ horizontal: skeletonStates.newer }"
          @goods-click="handleGoodsClick" />
      </SectionContainer>

      <SectionContainer v-if="skeletonStates.hotProducts || hotProductsList.length > 0" title="爆款好物">
        <WaterfallSection :waterfall-goods-list="hotProductsList" :waterfall-loading="hotProductsLoading"
          :waterfall-finished="hotProductsFinished" :waterfall-button-can-show="hotProductsButtonCanShow"
          :waterfall-render-complete="true" :skeleton-states="{ waterfall: skeletonStates.hotProducts }"
          @goods-click="handleGoodsClick" @load-more="handleHotProductsLoadMore" />
      </SectionContainer>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { throttle } from 'lodash-es'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import SectionContainer from '@views/Home/components/SectionContainer.vue'
import WaterfallSection from '@components/GoodsCommon/WaterfallSection.vue'
import HorizontalScrollSection from '@views/Home/components/HorizontalScrollSection.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { closeToast, showLoadingToast } from 'vant'

// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  getHeaderBannerList,
  getIconList,
  transformGoodsData
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 限量商品数据
const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedButtonCanShow = ref(false)

// 新品数据
const newerList = ref([])
const newerLoading = ref(false)

// 热门商品数据
const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsButtonCanShow = ref(false)

// 扩展骨架屏状态
skeletonStates.value = {
  ...skeletonStates.value,
  limited: true,
  newer: true,
  hotProducts: true
}

// 获取限量商品列表
const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
      limitedCurrentPage.value++
    } else {
      limitedList.value = newItems
      skeletonStates.value.limited = false
      limitedButtonCanShow.value = true
      limitedCurrentPage.value = 2
    }

    limitedFinished.value = json.length === 0
  } else {
    limitedFinished.value = true
    if (!isLoadMore) {
      skeletonStates.value.limited = false
    }
  }

  limitedLoading.value = false
}

// 加载更多限量商品
const handleLimitedLoadMore = throttle(() => {
  if (!limitedFinished.value && !limitedLoading.value) {
    getLimitedList(true)
  }
}, 300)

// 获取新品列表
const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    newerList.value = json.map(transformGoodsData)
  }

  skeletonStates.value.newer = false
  newerLoading.value = false
}

// 获取热门商品列表
const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  if (isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      hotProductsCurrentPage.value++
    } else {
      hotProductsList.value = newItems
      skeletonStates.value.hotProducts = false
      hotProductsButtonCanShow.value = true
      hotProductsCurrentPage.value = 2
    }

    hotProductsFinished.value = json.length === 0
  } else {
    hotProductsFinished.value = true
    if (!isLoadMore) {
      skeletonStates.value.hotProducts = false
    }
  }

  hotProductsLoading.value = false
}

// 加载更多热门商品
const handleHotProductsLoadMore = throttle(() => {
  if (!hotProductsFinished.value && !hotProductsLoading.value) {
    getHotProductsList(true)
  }
}, 300)

onMounted(() => {
  getHeaderBannerList()
  getIconList()
  getLimitedList(false)
  getNewerList()
  getHotProductsList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  //padding: @radius-12;
  //box-sizing: border-box;
}
</style>
