<template>
  <div class="zns-container">
    <main class="zns-content-card">
      <header v-if="shouldShowBanner" class="zns-banner-section">
        <Transition name="skeleton-fade" mode="out-in">
          <BannerSkeleton v-if="bannerLoading" key="banner-skeleton" />
          <GoodsSwiper
            v-else
            key="banner-content"
            :image-list="swiperList"
            mode="landscape"
            pagination-type="fraction"
            :autoplay="true"
            :loop="true"
            @image-click="handleBannerClick"
          />
        </Transition>
      </header>

      <section class="zns-description">
        <p class="zns-desc-main">
          帮扶商城是沃钱包的助农消费帮扶电商平台，于2019年12月上线，接入消费帮扶县47个，上架商品2500个，诚邀各供应商入驻。
        </p>
        <p class="zns-desc-guide">
          体验路径：进入中国联通APP-我的-我的钱包-帮扶商城
        </p>
      </section>
    </main>

     <div class="zns-action-wrapper">
      <WoButton
        type="gradient"
        size="large"
        @click="handleRecommendClick"
      >
        推荐供应商
      </WoButton>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { isWopay, isUnicom } from 'commonkit'
import { showToast } from 'vant'
import { memoize } from 'lodash-es'
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import { getBannerList } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

const router = useRouter()

const swiperList = ref([])
const bannerLoading = ref(true)

const shouldShowBanner = computed(() =>
  bannerLoading.value || swiperList.value.length > 0
)

const getChannelFilter = memoize(() => {
  if (isUnicom) {
    return (item) => item.channelType === '1'
  } else if (isWopay) {
    return (item) => item.channelType === '0'
  } else {
    return (item) => item.channelType === '2'
  }
})

const transformBannerData = (bannerData) => {
  return bannerData.map((item, index) => ({
    id: item.id || index,
    url: item.imgUrl,
    alt: item.bannerChName || `轮播图 ${index + 1}`,
    title: item.bannerChName,
    linkUrl: item.url
  }))
}

const handleRecommendClick = () => {
  router.push('/digitalVillage/supplierRecommend')
}

const handleBannerClick = ({ item }) => {
  if (item?.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const loadBannerData = async () => {
  const params = {
    bizCode: getBizCode('QUERY'),
    showPage: '2'
  }

  try {
    const [err, json] = await getBannerList(params)

    if (err) {
      console.error('获取轮播图失败:', err.msg)
      showToast(err.msg)
      swiperList.value = []
      return
    }

    const bannerData = json || []
    const channelFilter = getChannelFilter()
    const filteredData = bannerData.filter(channelFilter)

    swiperList.value = transformBannerData(filteredData)
  } catch (error) {
    console.error('获取轮播图异常:', error)
    swiperList.value = []
  } finally {
    bannerLoading.value = false
  }
}

onMounted(() => {
  loadBannerData()
})
</script>

<style scoped lang="less">
.zns-container {
  padding: 17px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.zns-content-card {
  background: #ffffff;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
}

.zns-banner-section {
  position: relative;

  :deep(.goods-swiper) {
    border-radius: 8px 8px 0 0;
  }
}

.zns-description {
  padding: 20px;
}

.zns-desc-main {
  font-size: 14px;
  color: #5a6066;
  line-height: 1.6;
  font-weight: 400;
  margin: 0 0 20px 0;
  text-align: justify;
}

.zns-desc-guide {
  font-size: 13px;
  color: #5a6066;
  line-height: 1.5;
  font-weight: 400;
  margin: 0;
  text-align: justify;
}

.zns-action-wrapper {
  padding: 30px 0 20px;
  display: flex;
  justify-content: center;
}

.zns-recommend-btn {
  background: linear-gradient(135deg, #ffa033 0%, #ff6d33 100%);
  box-shadow: 0 5px 9px 0 rgba(255, 158, 51, 0.4);
  border-radius: 23px;
  border: none;
  height: 46px;
  min-width: 200px;
  font-size: 17px;
  color: #ffffff;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 7px 12px 0 rgba(255, 158, 51, 0.5);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 3px 6px 0 rgba(255, 158, 51, 0.4);
  }

  &:focus-visible {
    outline: 2px solid #ffa033;
    outline-offset: 2px;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
