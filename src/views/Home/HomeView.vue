<template>
  <MainLayout :showNav="showNav">
    <component :is="currentHomeComponent" v-if="currentHomeComponent" />
    <div v-else class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h3 class="error-title">未找到对应的业务</h3>
        <p class="error-message">请联系统管理员</p>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, defineAsyncComponent } from 'vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { getBizCode } from '@utils/curEnv.js'

const bizCode = ref(getBizCode())

// 懒加载组件映射表
const componentMap = {
  fupin: defineAsyncComponent(() => import('@views/Home/BFHome/BFHomeView.vue')),
  ziying: defineAsyncComponent(() => import('@views/Home/ZYHome/ZYHomeView.vue')),
  welfaresop: defineAsyncComponent(() => import('@views/Home/WelfareHome/WelfareHomeView.vue')),
  labor: defineAsyncComponent(() => import('@views/Home/LaborHome/LaborHomeView.vue')),
  ygjd: defineAsyncComponent(() => import('@views/Home/YgjdHome/YgjdHomeView.vue')),
  zq: defineAsyncComponent(() => import('@views/Home/ZQHome/ZQHomeView.vue')),
  lnzx: defineAsyncComponent(() => import('@views/Home/LnxzHome/LnzxHomeView.vue')),
  sfzn: defineAsyncComponent(() => import('@views/Home/SfznHome/SfznHomeView.vue'))
}

// 根据bizCode动态获取对应组件
const currentHomeComponent = computed(() => {
  return componentMap[bizCode.value] || null
})

// 控制导航显示：sfzn业务不显示导航，没有对应模块时也不显示导航
const showNav = computed(() => {
  return bizCode.value !== 'sfzn' && currentHomeComponent.value !== null
})
</script>

<style lang="less" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: @bg-color-gray;
  padding: @padding-page;
}

.error-content {
  background: @bg-color-white;
  border-radius: @radius-12;
  padding: 48px 40px;
  text-align: center;
  box-shadow: 0 20px 40px @mask-color-065;
  max-width: 480px;
  width: 100%;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: @gradient-orange-106;
    border-radius: 0 0 @radius-4 @radius-4;
  }
}

.error-icon {
  margin-bottom: 24px;

  svg {
    color: @theme-color;
    filter: drop-shadow(0 4px 8px fade(@theme-color, 20%));
  }
}

.error-title {
  .title-level-2();
  margin: 0 0 16px 0;
  letter-spacing: -0.5px;
}

.error-message {
  .text-body();
  color: @text-color-secondary;
  line-height: 1.6;
  margin: 0 0 20px 0;

  code {
    background: @bg-color-gray;
    color: @theme-color;
    padding: 4px 8px;
    border-radius: @radius-6;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: @font-size-14;
    font-weight: @font-weight-600;
  }
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-btn, .contact-btn {
  .button-base();
  padding: 12px 24px;
  border-radius: @radius-8;
  font-size: @font-size-14;
  font-weight: @font-weight-500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px fade(@theme-color, 15%);
  }

  &:active {
    transform: translateY(0);
  }
}

.retry-btn {
  background: @gradient-orange-106;
  color: @text-color-white;

  &:hover {
    background: @gradient-orange-dark;
    box-shadow: 0 4px 12px fade(@theme-color, 40%);
  }
}

.contact-btn {
  background: @bg-color-white;
  color: @text-color-secondary;
  border: 1px solid @divider-color-base;

  &:hover {
    background: @bg-color-gray;
    border-color: @text-color-tertiary;
  }
}

@media (max-width: 768px) {
  .error-container {
    padding: 16px;
  }

  .error-content {
    padding: 32px 24px;
  }

  .error-title {
    font-size: @font-size-20;
  }

  .error-message {
    font-size: @font-size-16;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .retry-btn, .contact-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
