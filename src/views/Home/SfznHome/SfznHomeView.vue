<template>
  <div class="sfzn-home">
    <section class="banner-section">
      <BannerSkeleton v-if="skeletonStates.banner" />

      <div v-else-if="imageList && imageList.length > 0" class="banner-wrapper">
        <GoodsSwiper
          :image-list="imageList"
          :autoplay="true"
          :autoplay-delay="3000"
          :loop="true"
          :show-pagination="true"
          mode="landscape"
          @image-click="onImageClick"
        />
      </div>
    </section>

    <section class="category-section">
      <CategorySkeleton v-if="skeletonStates.category" />

      <div v-else-if="isEmpty" class="empty-state">
        <div class="empty-text">暂无分类数据</div>
      </div>

      <div v-else class="category-list">
        <article
          v-for="item in secondList"
          :key="item.id"
          class="category-item"
          :style="getCategoryItemStyle(item.img)"
        >
          <header class="category-header">
            <h2 class="category-title">{{ item.name }}</h2>
          </header>

          <div class="category-content">
            <div class="subcategory-grid">
              <div
                v-for="subcategory in item.list"
                :key="subcategory.id"
                class="subcategory-item"
                @click="onSubcategoryClick(subcategory)"
              >
                <div class="subcategory-image-wrapper">
                  <img
                    class="subcategory-image"
                    :src="subcategory.img"
                    alt=""
                    loading="lazy"
                    @error="handleImageError"
                  />
                </div>
                <span class="subcategory-name">{{ subcategory.name }}</span>
              </div>
            </div>
          </div>
        </article>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import BannerSkeleton from '@/views/Home/components/Skeleton/BannerSkeleton.vue'
import CategorySkeleton from '../components/Skeleton/CategorySkeleton.vue'
import { getBannerInfo } from '@/api/interface/bannerIcon'
import { getClassification } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { showToast } from 'vant'
import { categoryPid } from '@utils/storage.js'

const skeletonStates = ref({
  banner: true,
  category: true
})

const imageList = ref([])

const secondList = ref([])
const loading = ref(false)

const isEmpty = computed(() => !loading.value && secondList.value.length === 0)

const route = useRoute()
const router = useRouter()

const getCategoryItemStyle = (imgUrl) => {
  if (!imgUrl) return {}
  return {
    backgroundImage: `url(${imgUrl})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '100% 100%',
    backgroundPosition: 'center'
  }
}

const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

const initSwiper = async () => {
  try {
    const params = {
      showPage: '1',
      bizCode: getBizCode('GOODS')
    }

    const [err, json] = await getBannerInfo(params)

    if (err) {
      console.error('获取轮播数据失败:', err.msg)
      showToast(err.msg)
      return
    }

    if (json && json.length > 0) {
      imageList.value = json.map((item, index) => ({
        id: item.id || index,
        url: item.imgUrl,
        alt: item.bannerChName || `轮播图 ${index + 1}`,
        title: item.bannerChName,
        linkUrl: item.url
      }))
    }
  } catch (error) {
    console.error('轮播初始化失败:', error)
    showToast('轮播初始化失败')
  } finally {
    await nextTick()
    setTimeout(() => {
      skeletonStates.value.banner = false
    }, 300)
  }
}

const onImageClick = debounce(({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}, 300)

const fetchClassification = async (id) => {
  try {
    const [err, json] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      console.error('获取分类数据失败:', err.msg)
      return []
    }

    return json || []
  } catch (error) {
    console.error('获取分类数据失败:', error)
    return []
  }
}

const processCategoriesInBatches = async (categories, batchSize = 3) => {
  const results = []

  for (let i = 0; i < categories.length; i += batchSize) {
    const batch = categories.slice(i, i + batchSize)
    const batchPromises = batch.map(async (item, batchIndex) => {
      const actualIndex = i + batchIndex
      try {
        const thirdList = await fetchClassification(item.id)
        const sortedThirdList = thirdList.sort((a, b) => b.pos - a.pos)
        return { index: actualIndex, list: sortedThirdList }
      } catch (error) {
        console.error(`获取三级分类失败 (ID: ${item.id}):`, error)
        return { index: actualIndex, list: [] }
      }
    })

    const batchResults = await Promise.allSettled(batchPromises)
    results.push(...batchResults)
  }

  return results
}

const initCategoryData = async () => {
  if (loading.value) return

  try {
    loading.value = true

    const secondCategories = await fetchClassification(categoryPid.get())

    if (!secondCategories || secondCategories.length === 0) {
      secondList.value = []
      return
    }

    const sortedSecondCategories = secondCategories.sort((a, b) => b.pos - a.pos)

    const transList = sortedSecondCategories.map(item => ({
      id: item.id,
      name: item.name,
      img: item.img,
      list: []
    }))

    secondList.value = transList

    const batchResults = await processCategoriesInBatches(sortedSecondCategories)

    batchResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        const { index, list } = result.value
        if (secondList.value[index]) {
          secondList.value[index].list = list
        }
      }
    })

  } catch (error) {
    console.error('初始化分类数据失败:', error)
    showToast('初始化分类数据失败')
    secondList.value = []
  } finally {
    loading.value = false
    await nextTick()
    setTimeout(() => {
      skeletonStates.value.category = false
    }, 300)
  }
}

const onSubcategoryClick = debounce((item) => {
  const timestamp = Date.now()
  router.push({
    path: `/goodslist/${item.id}`,
    query: {
      ...route.query,
      timestamp
    }
  })
}, 300)

const initializeApp = async () => {
  try {
    if (route.query.category_pid) {
      categoryPid.set(route.query.category_pid)
    }

    await Promise.allSettled([
      initSwiper(),
      initCategoryData()
    ])
  } catch (error) {
    console.error('应用初始化失败:', error)
    showToast('页面初始化失败')
  }
}

onMounted(async () => {
  await initializeApp()
})
</script>

<style lang='less' scoped>
.sfzn-home {
  background-color: @bg-color-gray;
  height: 100%;
  overflow: auto;
}

.banner-section {
  padding: 4px @padding-page 0;

  .banner-wrapper {
    border-radius: @radius-12;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.category-section {
  padding: 8px @padding-page;

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: @bg-color-white;
    border-radius: @radius-12;
    margin-top: 8px;

    .empty-text {
      font-size: @font-size-16;
      color: @text-color-tertiary;
      font-weight: @font-weight-400;
    }
  }
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  background-color: @bg-color-white;
  border-radius: @radius-12;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
}

.category-header {
  padding: 10px;
  //background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  //backdrop-filter: blur(10px);

  .category-title {
    font-size: @font-size-18;
    color: @text-color-primary;
    font-weight: @font-weight-500;
    line-height: 1.2;
    margin: 0;
    .ellipsis();
  }
}

.category-content {
  padding: 10px 5px;
}

.subcategory-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  border-radius: @radius-8;
  transition: all 0.2s ease;
  width: calc(33.333% - 14px);
  min-width: 80px;

  &:hover {
    background-color: @bg-color-tips;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    background-color: darken(@bg-color-tips, 5%);
  }
}

.subcategory-image-wrapper {
  width: 68px;
  height: 68px;
  border-radius: @radius-8;
  overflow: hidden;
  margin-bottom: 5px;
  background-color: @bg-color-gray;
  display: flex;
  align-items: center;
  justify-content: center;

  .subcategory-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.subcategory-name {
  font-size: @font-size-14;
  color: @text-color-primary;
  font-weight: @font-weight-400;
  line-height: 1.2;
  .multi-ellipsis(2);
  max-width: 100%;
  word-break: break-all;
}
</style>
