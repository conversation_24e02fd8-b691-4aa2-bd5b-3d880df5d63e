<template>
  <div
    v-if="skeletonStates.horizontal || goodsList.length > 0"
    class="home-horizontal-scroll-container"
    :style="backgroundStyle"
  >
    <transition name="skeleton-fade" mode="out-in">
      <HorizontalScrollSkeleton v-if="skeletonStates.horizontal" :skeleton-count="5" key="horizontal-skeleton" />
      <div v-else-if="goodsList.length > 0" key="horizontal-content" class="home-horizontal-scroll-content">
        <div v-if="showHotZone" class="home-hot-zone" @click="handleHotZoneClick">
          <!-- 热区内容可以根据需要添加 -->
        </div>

        <div class="home-horizontal-scroll-wrapper">
          <div
            class="home-goods-item"
            v-for="item in goodsList"
            :key="item.goodsId"
            @click="handleGoodsClick(item)"
          >
            <component
              :is="cardComponent"
              :goods-info="item"
              @click="handleGoodsClick(item)"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import HorizontalScrollSkeleton from '@views/Home/components/Skeleton/HorizontalScrollSkeleton.vue'
import ProductCard from '@views/Home/components/ProductCard.vue'
import ProductCardMini from '@views/Home/components/ProductCardMini.vue'

const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  showHotZone: {
    type: Boolean,
    default: false
  },
  cardType: {
    type: String,
    default: 'normal', // 'normal' | 'mini'
    validator: (value) => ['normal', 'mini'].includes(value)
  },
  skeletonStates: {
    type: Object,
    default: () => ({
      horizontal: true
    })
  }
})

const emit = defineEmits(['goods-click', 'hot-zone-click'])

const { goodsList, backgroundImage, showHotZone, cardType, skeletonStates } = toRefs(props)

const backgroundStyle = computed(() => {
  return backgroundImage.value
    ? {
        backgroundImage: `url(${backgroundImage.value})`,
        backgroundSize: '100% 100%',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {}
})

const cardComponent = computed(() => {
  return cardType.value === 'mini' ? ProductCardMini : ProductCard
})

const handleGoodsClick = (goodsInfo) => {
  emit('goods-click', goodsInfo)
}

const handleHotZoneClick = () => {
  emit('hot-zone-click')
}
</script>

<style scoped lang="less">
.home-horizontal-scroll-container {
  position: relative;
  min-height: 180px;
  display: flex;
  align-items: center;
  border-radius: @radius-12;
  margin: @radius-8 @radius-12;
  overflow: hidden;
  box-sizing: border-box;

  .home-horizontal-scroll-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
  }

  .home-hot-zone {
    flex: 1;
    min-height: 40px;
    cursor: pointer;
    box-sizing: border-box;

    &:hover {
      opacity: 0.9;
    }
  }

  .home-horizontal-scroll-wrapper {
    display: flex;
    gap: @radius-12;
    overflow-x: auto;
    scroll-behavior: smooth;
    width: 100%;
    box-sizing: border-box;
    .no-scrollbar();

    .home-goods-item {
      flex: 0 0 130px;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: @radius-12;
      }
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
