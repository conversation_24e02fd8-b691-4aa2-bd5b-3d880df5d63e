<template>
  <div class="grid-menu-skeleton">
    <div class="skeleton-grid-container">
      <div v-for="i in 5" :key="i" class="skeleton-grid-item">
        <div class="skeleton-icon"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.grid-menu-skeleton {

  background: #ffffff;
  border-radius: 12px;
  margin: 8px 12px;

  padding: 5px;

  .skeleton-grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .skeleton-grid-item {
      width: calc(20% - 6.4px);
      display: flex;
      flex-direction: column;
      align-items: center;

      padding: 6px;
      text-align: center;
      box-sizing: border-box;

      .skeleton-icon {
        .skeleton-base();

        width: 34px;
        height: 34px;
        border-radius: 4px;

        margin-bottom: 8px;
      }

      .skeleton-title {
        .skeleton-base();
        width: 80%;

        height: 12px;
        margin-bottom: 2px;
      }

      .skeleton-subtitle {
        .skeleton-base();
        width: 60%;

        height: 10px;
      }
    }
  }
}


@media (max-width: 375px) {
  .grid-menu-skeleton {
    .skeleton-grid-container {
      .skeleton-grid-item {

        padding: 6px;

        .skeleton-icon {

          width: 34px;
          height: 34px;
          margin-bottom: 8px;
        }

        .skeleton-title {
          height: 12px;
          margin-bottom: 2px;
        }

        .skeleton-subtitle {
          height: 10px;
        }
      }
    }
  }
}
</style>
