<template>
  <div class="horizontal-scroll-skeleton">
    <div class="skeleton-scroll-wrapper">
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-title short"></div>
          <div class="skeleton-details">
            <div class="skeleton-price"></div>
            <div class="skeleton-sales"></div>
          </div>
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.horizontal-scroll-skeleton {

  position: relative;
  min-height: 180px;
  display: flex;
  align-items: center;

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    scroll-behavior: smooth;
    width: 100%;


    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;

    .skeleton-item {
      flex: 0 0 160px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #f0f0f0;
      cursor: pointer;

      height: 280px;


      &:last-child {
        margin-right: 12px;
      }

      .skeleton-image {
        .skeleton-base();
        width: 100%;
        height: 180px;
        border-radius: 8px 8px 0 0;
        background-color: #fafafa;
      }

      .skeleton-content {

        padding: 12px;

        .skeleton-title {
          .skeleton-base();

          height: 14px;
          margin-bottom: 8px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 8px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          gap: 8px;

          .skeleton-price {
            .skeleton-base();

            height: 16px;
            width: 60px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            .skeleton-base();

            height: 12px;
            width: 50px;
            border-radius: 6px;
          }
        }

        .skeleton-spec {
          .skeleton-base();

          height: 12px;
          width: 80%;
          border-radius: 4px;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .horizontal-scroll-skeleton {
    .skeleton-scroll-wrapper {
      gap: 12px;

      .skeleton-item {
        flex: 0 0 160px;
        height: 280px;

        .skeleton-image {
          height: 180px;
        }

        .skeleton-content {
          padding: 12px;

          .skeleton-title {
            height: 14px;
            margin-bottom: 8px;

            &.short {
              margin-bottom: 8px;
            }
          }

          .skeleton-details {
            .skeleton-price {
              height: 16px;
              width: 60px;
            }

            .skeleton-sales {
              height: 12px;
              width: 50px;
            }
          }

          .skeleton-spec {
            height: 12px;
          }
        }
      }
    }
  }
}
</style>
