<template>
  <div class="goods-card-mini">
    <div class="goods-image">
      <img :src="goodsInfo.image" :alt="goodsInfo.name" loading="lazy" decoding="async" />
    </div>

    <div class="goods-info">
      <div class="goods-name">{{ goodsInfo.name }}</div>
      <div class="goods-details">
        <span class="goods-price">{{ goodsInfo.price }}</span>
        <span class="goods-sales" v-if="goodsInfo.sales > 0">销量: {{ goodsInfo.sales }}</span>
      </div>
      <div class="goods-spec" v-if="goodsInfo.spec">
        {{ goodsInfo.spec }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

// 定义组件props
const props = defineProps({
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',
      name: '',
      price: 0,
      sales: 0,
      spec: '',
    })
  }
})

const { goodsInfo } = toRefs(props)
</script>

<style scoped lang="less">
.goods-card-mini {
  background: @bg-color-white;
  border-radius: @radius-6;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 160px;
  min-width: 100px;
  display: flex;
  flex-direction: column;
}

.goods-image {
  position: relative;
  width: 100%;
  //height: 120px;
  overflow: hidden;
  background: @bg-color-gray;
  flex: 1;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    background-color: @bg-color-gray;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: @radius-8;
  background: @bg-color-white;

  .goods-name {
    font-size: @font-size-12;
    font-weight: @font-weight-400;
    color: @text-color-primary;
    margin: 0 0 @radius-6 0;
    line-height: 1.3;
    .multi-ellipsis(2);
    text-decoration: none;
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @radius-4;

    .goods-price {
      color: @theme-color;
      font-size: @font-size-14;
      font-weight: @font-weight-700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: @font-size-11;
        font-weight: @font-weight-400;
        margin-right: 1px;
      }
    }

    .goods-sales {
      color: @text-color-tertiary;
      font-size: @font-size-11;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .goods-spec {
    color: @text-color-secondary;
    font-size: @font-size-11;
    line-height: 1.2;
    background: @bg-color-gray;
    padding: @radius-2 @radius-6;
    border-radius: @radius-2;
    margin-top: @radius-2;
    display: inline-block;
    max-width: 100%;
    .ellipsis();
  }
}
</style>
