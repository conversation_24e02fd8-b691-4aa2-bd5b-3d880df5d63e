<template>
  <div class="recommend-section" v-if="items.length > 0">
    <div class="title">
      <img
        v-for="(item, index) in items"
        :key="`title-${index}`"
        :src="item.fontImgUrl"
        :alt="`推荐标题${index + 1}`"
      />
    </div>
    <div class="commodity">
      <img
        v-for="(item, index) in items"
        :key="`commodity-${index}`"
        :src="item.actImgUrl"
        :alt="`推荐商品${index + 1}`"
        @click="$emit('imgClick', item.skipUrl)"
      />
    </div>
  </div>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    default: () => []
  }
})

defineEmits(['imgClick'])
</script>

<style lang="less" scoped>
.recommend-section {
  width: calc(50% - 5px);
  background: @bg-color-white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: @radius-10;
  margin-bottom: 10px;

  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 10px;
    // padding: 0 10px;

    img:first-child {
      width: 80px;
      height: auto;
    }

    img:last-child {
      width: 65px;
      height: auto;
    }
  }

  .commodity {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 10px;

    img {
      width: calc(50% - 5px);
      height: auto;
      cursor: pointer;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: @opacity-07;
      }
    }
  }
}
</style>
