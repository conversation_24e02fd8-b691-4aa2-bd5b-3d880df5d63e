<template>
  <div class="goods-swiper-container" :class="[`mode-${mode}`]">
    <!-- 主轮播容器 -->
    <div class="swiper-wrapper-container" ref="swiperContainer">
      <swiper ref="swiperRef" :modules="swiperModules" :slides-per-view="slidesPerView" :space-between="spaceBetween"
        :centered-slides="centeredSlides" :loop="enableLoop" :autoplay="autoplayConfig" :lazy="lazyLoading"
        :preload-images="!lazyLoading" :watch-slides-progress="true" :observer="true" :observe-parents="true"
        :resistance-ratio="0.85" :threshold="5" :long-swipes-ratio="0.3" :speed="transitionSpeed" :effect="effect"
        :grab-cursor="true" :keyboard="{ enabled: true }" :a11y="{ enabled: true }" class="goods-swiper"
        :class="swiperClasses" @swiper="onSwiperInit" @slide-change="onSlideChange"
        @slide-change-transition-start="onSlideChangeStart" @slide-change-transition-end="onSlideChangeEnd"
        @progress="onProgress" @reach-beginning="onReachBeginning" @reach-end="onReachEnd" @touch-start="onTouchStart"
        @touch-end="onTouchEnd">
        <!-- 图片幻灯片 -->
        <swiper-slide v-for="(item, index) in imageList" :key="getSlideKey(item, index)" class="goods-slide"
          :class="getSlideClasses(item, index)">
          <div class="slide-content" :class="getContentClasses(item, index)">
            <!-- 图片容器 -->
            <div class="image-container" :class="getImageContainerClasses(item, index)">
              <!-- 加载占位符 -->
              <div v-if="!imageLoadStates[index]?.loaded" class="image-placeholder"
                :class="{ 'loading': imageLoadStates[index]?.loading }">
                <div class="placeholder-content">
                  <div v-if="imageLoadStates[index]?.loading" class="loading-spinner">
                    <div class="spinner-ring"></div>
                  </div>
                  <div v-else class="placeholder-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"
                        fill="currentColor" opacity="0.3" />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- 实际图片 -->
              <img :src="shouldLoadImage(index) ? item.url : undefined" :data-src="item.url"
                :alt="item.alt || `商品图片 ${index + 1}`" :class="getImageClasses(item, index)" class="goods-image"
                :loading="getImageLoading(index)" :fetchpriority="getImagePriority(index)"
                @load="onImageLoad($event, index)" @error="onImageError($event, index)"
                @click="onImageClick(item, index)" />

              <!-- 图片遮罩层（可选） -->
              <div v-if="showImageOverlay" class="image-overlay" :class="{ 'active': currentIndex === index }">
                <slot name="image-overlay" :item="item" :index="index" :active="currentIndex === index">
                  <!-- 默认遮罩内容 -->
                </slot>
              </div>
            </div>

            <!-- 图片信息（可选） -->
            <div v-if="showImageInfo" class="image-info">
              <slot name="image-info" :item="item" :index="index">
                <div v-if="item.title" class="image-title">{{ item.title }}</div>
                <div v-if="item.description" class="image-description">{{ item.description }}</div>
              </slot>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>

    <!-- 分页器 -->
    <div v-if="showPagination && imageList.length > 1" class="swiper-pagination-container" :class="paginationClasses">
      <slot name="pagination" :current="currentIndex" :total="imageList.length" :go-to="goToSlide">
        <!-- 圆点分页器 -->
        <div v-if="paginationType === 'bullets'" class="pagination-bullets">
          <button v-for="(_, index) in imageList" :key="index" class="pagination-bullet"
            :class="{ 'active': currentIndex === index }" :aria-label="`转到第 ${index + 1} 张图片`"
            @click="goToSlide(index)">
            <span class="bullet-inner"></span>
          </button>
        </div>

        <!-- 分数分页器 -->
        <div v-else-if="paginationType === 'fraction'" class="pagination-fraction">
          <span class="fraction-current">{{ currentIndex + 1 }}</span>
          <span class="fraction-separator">/</span>
          <span class="fraction-total">{{ imageList.length }}</span>
        </div>

        <!-- 进度条分页器 -->
        <div v-else-if="paginationType === 'progressbar'" class="pagination-progressbar">
          <div class="progressbar-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>

        <!-- 缩略图分页器 -->
        <div v-else-if="paginationType === 'thumbnails'" class="pagination-thumbnails">
          <button v-for="(item, index) in imageList" :key="index" class="thumbnail-item"
            :class="{ 'active': currentIndex === index }" :aria-label="`转到第 ${index + 1} 张图片`"
            @click="goToSlide(index)">
            <img :src="item.thumbnail || item.url" :alt="item.alt || `缩略图 ${index + 1}`" class="thumbnail-image" />
          </button>
        </div>
      </slot>
    </div>

    <!-- 加载更多指示器（可选） -->
    <div v-if="showLoadMore && hasMore" class="load-more-indicator">
      <slot name="load-more" :loading="loadingMore" :load="loadMore">
        <button class="load-more-button" :disabled="loadingMore" @click="loadMore">
          <span v-if="!loadingMore">加载更多</span>
          <span v-else class="loading-text">
            <div class="loading-spinner small">
              <div class="spinner-ring"></div>
            </div>
            加载中...
          </span>
        </button>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, shallowRef } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, EffectFade, EffectCoverflow, Keyboard, A11y } from 'swiper/modules'
import { debounce, throttle } from 'lodash-es'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/effect-fade'
import 'swiper/css/effect-coverflow'
import 'swiper/css/keyboard'
import 'swiper/css/a11y'

// Props 定义
const props = defineProps({
  // 图片列表
  imageList: {
    type: Array,
    default: () => [],
    validator: (value) => Array.isArray(value)
  },

  // 显示模式：square(方图) | landscape(长图)
  mode: {
    type: String,
    default: 'square',
    validator: (value) => ['square', 'landscape'].includes(value)
  },

  // 自动播放
  autoplay: {
    type: [Boolean, Object],
    default: false
  },

  // 自动播放延迟时间（毫秒）
  autoplayDelay: {
    type: Number,
    default: 3000
  },

  // 是否循环播放
  loop: {
    type: Boolean,
    default: true
  },

  // 是否显示导航按钮
  showNavigation: {
    type: Boolean,
    default: true
  },

  // 是否显示分页器
  showPagination: {
    type: Boolean,
    default: true
  },

  // 分页器类型：bullets | fraction | progressbar | thumbnails
  paginationType: {
    type: String,
    default: 'bullets',
    validator: (value) => ['bullets', 'fraction', 'progressbar', 'thumbnails'].includes(value)
  },

  // 每页显示的幻灯片数量
  slidesPerView: {
    type: [Number, String],
    default: 1
  },

  // 幻灯片之间的间距
  spaceBetween: {
    type: Number,
    default: 0
  },

  // 是否居中显示
  centeredSlides: {
    type: Boolean,
    default: false
  },

  // 过渡效果：slide | fade | coverflow
  effect: {
    type: String,
    default: 'slide',
    validator: (value) => ['slide', 'fade', 'coverflow'].includes(value)
  },

  // 过渡速度（毫秒）
  speed: {
    type: Number,
    default: 300
  },

  // 是否启用懒加载
  lazy: {
    type: Boolean,
    default: true
  },

  // 预加载图片数量
  preloadImages: {
    type: Number,
    default: 2
  },

  // 自定义高度
  height: {
    type: [String, Number],
    default: null
  },

  // 自定义宽度
  width: {
    type: [String, Number],
    default: null
  },

  // 是否显示图片遮罩层
  showImageOverlay: {
    type: Boolean,
    default: false
  },

  // 是否显示图片信息
  showImageInfo: {
    type: Boolean,
    default: false
  },

  // 是否显示加载更多
  showLoadMore: {
    type: Boolean,
    default: false
  },

  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: false
  },

  // 是否正在加载更多
  loadingMore: {
    type: Boolean,
    default: false
  },

  // 响应式断点配置
  breakpoints: {
    type: Object,
    default: () => ({})
  },

  // 无障碍标签
  prevButtonLabel: {
    type: String,
    default: '上一张'
  },

  nextButtonLabel: {
    type: String,
    default: '下一张'
  }
})

// Emits 定义
const emit = defineEmits([
  'slide-change',
  'image-click',
  'image-load',
  'image-error',
  'swiper-init',
  'reach-beginning',
  'reach-end',
  'load-more',
  'touch-start',
  'touch-end'
])

// 响应式数据
const swiperRef = shallowRef(null)
const swiperContainer = ref(null)
const swiperInstance = shallowRef(null)
const currentIndex = ref(0)
const isAtBeginning = ref(true)
const isAtEnd = ref(false)
const isTransitioning = ref(false)
const progress = ref(0)

// 图片加载状态管理
const imageLoadStates = ref({})
const loadedImageUrls = new Set()

// 性能优化相关
const visibleRange = ref({ start: 0, end: 2 })
const intersectionObserver = ref(null)
const resizeObserver = ref(null)

// 移除响应式屏幕尺寸相关逻辑

// 计算属性
const swiperModules = computed(() => {
  const modules = [Keyboard, A11y]

  if (props.autoplay) modules.push(Autoplay)
  // if (props.lazy) modules.push(Lazy)
  if (props.effect === 'fade') modules.push(EffectFade)
  if (props.effect === 'coverflow') modules.push(EffectCoverflow)

  return modules
})

const enableLoop = computed(() => {
  return props.loop && props.imageList.length > 1
})

const lazyLoading = computed(() => {
  return props.lazy && props.imageList.length > 3
})

const transitionSpeed = computed(() => {
  return isTransitioning.value ? props.speed : props.speed
})

const autoplayConfig = computed(() => {
  if (!props.autoplay || props.imageList.length <= 1) return false

  if (typeof props.autoplay === 'object') {
    return {
      delay: props.autoplayDelay,
      disableOnInteraction: false,
      pauseOnMouseEnter: true,
      ...props.autoplay
    }
  }

  return {
    delay: props.autoplayDelay,
    disableOnInteraction: false,
    pauseOnMouseEnter: true
  }
})

const slidesPerView = computed(() => {
  if (typeof props.slidesPerView === 'number') {
    return props.slidesPerView
  }

  // 固定配置，去除响应式
  if (props.mode === 'landscape') {
    return 1
  } else {
    return 1
  }
})

const spaceBetween = computed(() => {
  if (props.spaceBetween > 0) {
    return props.spaceBetween
  }

  // 固定间距，去除响应式
  return props.mode === 'landscape' ? 16 : 12
})

const swiperClasses = computed(() => {
  return [
    `swiper-${props.mode}`,
    `swiper-effect-${props.effect}`,
    {
      'swiper-transitioning': isTransitioning.value
    }
  ]
})

const paginationClasses = computed(() => {
  return [
    `pagination-${props.mode}`
  ]
})

const progressPercentage = computed(() => {
  if (props.imageList.length <= 1) return 100
  return ((currentIndex.value + 1) / props.imageList.length) * 100
})

// 容器样式（暂时注释，后续可能会用到）
// const containerStyles = computed(() => {
//   const styles = {}

//   if (props.height) {
//     styles.height = typeof props.height === 'number' ? `${props.height}px` : props.height
//   }

//   if (props.width) {
//     styles.width = typeof props.width === 'number' ? `${props.width}px` : props.width
//   }

//   return styles
// })

// 图片相关计算属性
const shouldLoadImage = (index) => {
  if (!lazyLoading.value) return true

  const range = visibleRange.value
  return index >= range.start && index <= range.end
}

const getImageLoading = (index) => {
  if (index === 0) return 'eager'
  if (index <= props.preloadImages) return 'eager'
  return 'lazy'
}

const getImagePriority = (index) => {
  if (index === 0) return 'high'
  if (index <= props.preloadImages) return 'high'
  return 'auto'
}

const getSlideKey = (item, index) => {
  return item.id || item.url || index
}

const getSlideClasses = (item, index) => {
  return [
    `slide-${props.mode}`,
    {
      'slide-active': currentIndex.value === index,
      'slide-prev': currentIndex.value === index - 1,
      'slide-next': currentIndex.value === index + 1,
      'slide-loading': imageLoadStates.value[index]?.loading,
      'slide-loaded': imageLoadStates.value[index]?.loaded,
      'slide-error': imageLoadStates.value[index]?.error
    }
  ]
}

const getContentClasses = (item, index) => {
  return [
    `content-${props.mode}`,
    {
      'content-active': currentIndex.value === index
    }
  ]
}

const getImageContainerClasses = (item, index) => {
  const aspectRatio = imageLoadStates.value[index]?.aspectRatio

  return [
    `image-container-${props.mode}`,
    {
      'aspect-square': props.mode === 'square',
      'aspect-landscape': props.mode === 'landscape',
      'aspect-portrait': aspectRatio && aspectRatio < 1,
      'aspect-wide': aspectRatio && aspectRatio > 1.5
    }
  ]
}

const getImageClasses = (item, index) => {
  const state = imageLoadStates.value[index]

  return [
    `image-${props.mode}`,
    {
      'image-loading': state?.loading,
      'image-loaded': state?.loaded,
      'image-error': state?.error,
      'image-active': currentIndex.value === index
    }
  ]
}

// 方法定义
const initImageLoadStates = () => {
  props.imageList.forEach((_, index) => {
    if (!imageLoadStates.value[index]) {
      imageLoadStates.value[index] = {
        loading: false,
        loaded: false,
        error: false,
        aspectRatio: null
      }
    }
  })
}

const updateVisibleRange = () => {
  const preload = props.preloadImages
  const start = Math.max(0, currentIndex.value - preload)
  const end = Math.min(props.imageList.length - 1, currentIndex.value + preload)

  visibleRange.value = { start, end }
}

const preloadImage = (url, index) => {
  if (loadedImageUrls.has(url)) {
    imageLoadStates.value[index] = {
      ...imageLoadStates.value[index],
      loaded: true,
      loading: false
    }
    return Promise.resolve()
  }

  return new Promise((resolve, reject) => {
    const img = new Image()

    imageLoadStates.value[index] = {
      ...imageLoadStates.value[index],
      loading: true,
      error: false
    }

    img.onload = () => {
      const aspectRatio = img.naturalWidth / img.naturalHeight

      imageLoadStates.value[index] = {
        ...imageLoadStates.value[index],
        loading: false,
        loaded: true,
        aspectRatio
      }

      loadedImageUrls.add(url)
      resolve({ width: img.naturalWidth, height: img.naturalHeight, aspectRatio })
    }

    img.onerror = () => {
      imageLoadStates.value[index] = {
        ...imageLoadStates.value[index],
        loading: false,
        error: true
      }
      reject(new Error(`Failed to load image: ${url}`))
    }

    img.src = url
  })
}

const preloadVisibleImages = async () => {
  const { start, end } = visibleRange.value
  const promises = []

  for (let i = start; i <= end; i++) {
    const item = props.imageList[i]
    if (item && item.url && !loadedImageUrls.has(item.url)) {
      promises.push(preloadImage(item.url, i).catch(() => { }))
    }
  }

  await Promise.allSettled(promises)
}

// Swiper 事件处理
const onSwiperInit = (swiper) => {
  swiperInstance.value = swiper
  emit('swiper-init', swiper)

  // 初始化状态
  updateSlideStates(swiper)
  updateVisibleRange()

  // 预加载可见图片
  nextTick(() => {
    preloadVisibleImages()
  })
}

const onSlideChange = (swiper) => {
  // 对于非循环模式，使用activeIndex；循环模式使用realIndex
  const newIndex = enableLoop.value && swiper.realIndex !== undefined
    ? swiper.realIndex
    : swiper.activeIndex

  if (newIndex !== currentIndex.value && newIndex >= 0 && newIndex < props.imageList.length) {
    currentIndex.value = newIndex
    updateSlideStates(swiper)
    updateVisibleRange()

    // 预加载新的可见图片
    preloadVisibleImages()

    emit('slide-change', {
      index: newIndex,
      item: props.imageList[newIndex],
      swiper
    })
  }
}

const onSlideChangeStart = () => {
  isTransitioning.value = true
}

const onSlideChangeEnd = () => {
  isTransitioning.value = false
}

const onProgress = (swiper, progressValue) => {
  progress.value = progressValue
}

const onReachBeginning = (swiper) => {
  isAtBeginning.value = true
  emit('reach-beginning', swiper)
}

const onReachEnd = (swiper) => {
  isAtEnd.value = true
  emit('reach-end', swiper)
}

const onTouchStart = (swiper, event) => {
  emit('touch-start', { swiper, event })
}

const onTouchEnd = (swiper, event) => {
  emit('touch-end', { swiper, event })
}

const updateSlideStates = (swiper) => {
  isAtBeginning.value = swiper.isBeginning
  isAtEnd.value = swiper.isEnd
}

// 图片事件处理
const onImageLoad = (event, index) => {
  const img = event.target
  const aspectRatio = img.naturalWidth / img.naturalHeight

  imageLoadStates.value[index] = {
    ...imageLoadStates.value[index],
    loading: false,
    loaded: true,
    aspectRatio
  }

  loadedImageUrls.add(img.src)

  emit('image-load', {
    index,
    item: props.imageList[index],
    dimensions: {
      width: img.naturalWidth,
      height: img.naturalHeight,
      aspectRatio
    }
  })
}

const onImageError = (event, index) => {
  imageLoadStates.value[index] = {
    ...imageLoadStates.value[index],
    loading: false,
    error: true
  }

  emit('image-error', {
    index,
    item: props.imageList[index],
    error: event
  })
}

const onImageClick = (item, index) => {
  emit('image-click', { item, index })
}

// 导航方法
const slidePrev = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slidePrev()
  }
}

const slideNext = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slideNext()
  }
}

const goToSlide = (index) => {
  if (swiperInstance.value && index >= 0 && index < props.imageList.length) {
    // 确保索引正确，避免跳页问题
    if (enableLoop.value) {
      swiperInstance.value.slideToLoop(index, 300, false)
    } else {
      swiperInstance.value.slideTo(index, 300, false)
    }
  }
}

// 加载更多
const loadMore = () => {
  if (!props.loadingMore && props.hasMore) {
    emit('load-more')
  }
}

// 性能优化方法
const handleResize = debounce(() => {
  if (swiperInstance.value) {
    swiperInstance.value.update()
  }
}, 150)

const handleVisibilityChange = throttle(() => {
  if (document.hidden) {
    // 页面隐藏时暂停自动播放
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.stop()
    }
  } else {
    // 页面显示时恢复自动播放
    if (swiperInstance.value?.autoplay && props.autoplay) {
      swiperInstance.value.autoplay.start()
    }
  }
}, 100)

// 初始化 Intersection Observer
const initIntersectionObserver = () => {
  if (!window.IntersectionObserver || !lazyLoading.value) return

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target
          // const index = parseInt(img.dataset.index) // 暂时不需要使用 index

          if (img.dataset.src && !img.src) {
            img.src = img.dataset.src
            img.removeAttribute('data-src')
          }

          intersectionObserver.value?.unobserve(img)
        }
      })
    },
    {
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// 初始化 Resize Observer
const initResizeObserver = () => {
  if (!window.ResizeObserver || !swiperContainer.value) return

  resizeObserver.value = new ResizeObserver(
    debounce(() => {
      if (swiperInstance.value) {
        swiperInstance.value.update()
      }
    }, 100)
  )

  resizeObserver.value.observe(swiperContainer.value)
}

// 清理方法
const cleanup = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }

  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }

  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
}

// 监听器
watch(
  () => props.imageList,
  (newList, oldList) => {
    if (newList.length !== oldList?.length) {
      initImageLoadStates()
      updateVisibleRange()

      nextTick(() => {
        if (swiperInstance.value) {
          swiperInstance.value.update()
        }
        preloadVisibleImages()
      })
    }
  },
  { deep: true }
)

watch(currentIndex, () => {
  updateVisibleRange()
})

// 生命周期钩子
onMounted(() => {
  initImageLoadStates()
  initIntersectionObserver()
  initResizeObserver()

  window.addEventListener('resize', handleResize, { passive: true })
  document.addEventListener('visibilitychange', handleVisibilityChange, { passive: true })

  // 预加载首屏图片
  nextTick(() => {
    preloadVisibleImages()
  })
})

onUnmounted(() => {
  cleanup()
})

// 暴露方法给父组件
defineExpose({
  swiper: swiperInstance,
  currentIndex,
  slidePrev,
  slideNext,
  goToSlide,
  preloadImage,
  updateVisibleRange
})
</script>

<style scoped lang="less">
// 变量定义
@primary-color: #ff7a0a;
@border-radius: 8px;
@transition-duration: 0.3s;
@box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
@loading-color: #f5f5f5;

// 主容器
.goods-swiper-container {
  position: relative;
  width: 100%;
  background: #fff;
  //border-radius: @border-radius;
  overflow: hidden;

  &.has-navigation {
    .swiper-wrapper-container {
      padding: 0 48px;
    }
  }

  // 方图模式
  &.mode-square {
    .swiper-wrapper-container {
      height: 100%;
    }
  }

  // 长图模式
  &.mode-landscape {
    .swiper-wrapper-container {
      height: 100%;
    }
  }
}

// Swiper 容器
.swiper-wrapper-container {
  position: relative;
  width: 100%;
  height: 100%;
}

// Swiper 主体
.goods-swiper {
  width: 100%;
  height: 100%;

  // 方图模式样式
  &.swiper-square {
    .swiper-slide {
      height: 100%;
    }
  }

  // 长图模式样式
  &.swiper-landscape {
    .swiper-slide {
      height: 100%;
    }
  }

  // 过渡效果
  &.swiper-effect-fade {
    .swiper-slide {
      transition: opacity @transition-duration ease;
    }
  }

  &.swiper-effect-coverflow {
    .swiper-slide {
      transition: transform @transition-duration ease;
    }
  }

  // 统一样式
  .goods-slide {
    border-radius: @border-radius;

    &:hover {
      transform: translateY(-2px);
      box-shadow: @box-shadow;
    }
  }
}

// 幻灯片
.goods-slide {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  //border-radius: @border-radius;
  overflow: hidden;
  transition: all @transition-duration ease;

  &.slide-loading {
    background: @loading-color;
  }

  &.slide-error {
    background: #fafafa;

    .image-placeholder {
      .placeholder-icon {
        color: #ccc;
      }
    }
  }

  // 方图模式
  &.slide-square {
    height: 100%;
  }

  // 长图模式
  &.slide-landscape {
    height: 100%;
  }
}

// 幻灯片内容
.slide-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &.content-square {
    .image-container {
      flex: 1;
    }
  }

  &.content-landscape {
    .image-container {
      flex: 1;
    }

    .image-info {
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(8px);
    }
  }
}

// 图片容器
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &.image-container-square {
    aspect-ratio: 1;
  }

  &.image-container-landscape {
    height: 100%;
  }

  &.aspect-portrait {
    .goods-image {
      width: auto;
      height: 100%;
    }
  }

  &.aspect-wide {
    .goods-image {
      width: 100%;
      height: auto;
    }
  }
}

// 图片占位符
.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: @loading-color;
  z-index: 1;

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #999;
  }

  .placeholder-icon {
    opacity: 0.5;
    transition: opacity @transition-duration ease;
  }

  &.loading {
    .placeholder-icon {
      display: none;
    }
  }
}

// 加载动画
.loading-spinner {
  position: relative;

  &.small {
    width: 16px;
    height: 16px;
  }

  .spinner-ring {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 122, 10, 0.2);
    border-top: 2px solid @primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;

    .small & {
      width: 16px;
      height: 16px;
      border-width: 1.5px;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 商品图片
.goods-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  transition: all @transition-duration ease;
  opacity: 0;

  &.image-loaded {
    opacity: 1;
  }

  &.image-error {
    opacity: 0.3;
  }

  &.image-square {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  &.image-landscape {
    object-fit: contain;
    width: 100%;
    height: 100%;
    background: #fff;
  }

  &:hover {
    transform: scale(1.02);
  }

  &.image-active {
    z-index: 2;
  }
}

// 图片遮罩层
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity @transition-duration ease;
  z-index: 3;

  &.active {
    opacity: 1;
  }
}

// 图片信息
.image-info {
  padding: 8px 12px;
  background: #fff;
  border-top: 1px solid #f0f0f0;

  .image-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .image-description {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
  }
}

// 导航按钮
.swiper-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.swiper-button {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all @transition-duration ease;
  pointer-events: auto;
  color: #333;

  &:hover {
    background: #fff;
    box-shadow: @box-shadow;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  svg {
    width: 20px;
    height: 20px;
  }
}

.swiper-button-prev {
  left: 8px;
}

.swiper-button-next {
  right: 8px;
}

// 分页器容器
.swiper-pagination-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;

  &.pagination-square {
    bottom: 12px;
  }

  &.pagination-landscape {
    bottom: 16px;
  }

  &.pagination-mobile {
    bottom: 8px;
  }
}

// 圆点分页器
.pagination-bullets {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  pointer-events: auto;

  .pagination-bullet {
    width: 6px;
    height: 6px;
    border: none !important;
    border-radius: 50% !important;
    background-color: rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    outline: none !important;
    padding: 0 !important;
    margin: 0 !important;

    &:hover {
      background-color: rgba(255, 122, 10, 0.7);
    }

    &.active {
      background-color: @primary-color;
      transform: scale(1.3);
    }

    .bullet-inner {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      border-radius: 50% !important;
      transform: translate(-50%, -50%);
      transition: all @transition-duration ease;
    }
  }


}

// 分数分页器
.pagination-fraction {
  position: absolute;
  bottom: -5px;
  right: 5px;
  pointer-events: auto;
  font-size: 13px;
  font-weight: 500;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  padding: 3px 10px;
  border-radius: 16px;
  backdrop-filter: blur(8px);

  .fraction-current {
    color: @primary-color;
  }

  .fraction-separator {
    margin: 0 4px;
    opacity: 0.7;
  }

  .fraction-total {
    opacity: 0.9;
  }


}

// 进度条分页器
.pagination-progressbar {
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  pointer-events: auto;

  .progressbar-fill {
    height: 100%;
    background: @primary-color;
    border-radius: 2px;
    transition: width @transition-duration ease;
  }
}

// 缩略图分页器
.pagination-thumbnails {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  margin: 0 auto;
  width: fit-content;
  max-width: 90%;
  overflow-x: auto;
  pointer-events: auto;

  .thumbnail-item {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all @transition-duration ease;

    &:hover {
      border-color: rgba(255, 122, 10, 0.5);
      transform: scale(1.05);
    }

    &.active {
      border-color: @primary-color;
      transform: scale(1.1);
    }

    .thumbnail-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }


}

// 加载更多指示器
.load-more-indicator {
  position: absolute;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;

  .load-more-button {
    padding: 8px 16px;
    background: @primary-color;
    color: #fff;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all @transition-duration ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      background: darken(@primary-color, 10%);
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }

    .loading-text {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }
}

// 统一样式，无响应式区别
.goods-swiper-container {
  &.mode-square {
    .goods-slide {
      &:not(.slide-active) {
        transform: scale(0.95);
        opacity: 0.8;
      }
    }
  }
}

.swiper-navigation {
  .swiper-button {
    &:hover {
      background: rgba(255, 255, 255, 0.95);
    }
  }
}
</style>
