<template>
  <div class="goods-list-layout">
    <GoodsSkeletonLoader
      v-if="isLoading"
      :is-waterfall="isWaterfall"
      :skeleton-count="isWaterfall ? 4 : 3"
    />

    <section v-show="goodsList.length > 0 && !isWaterfall" class="list-layout">
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <ul class="goods-list-container">
          <ProductListItem
            v-for="(item, index) in goodsList"
            :key="`goods-${item.skuId || index}`"
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </ul>
      </van-list>
    </section>

    <section v-show="goodsList.length > 0 && isWaterfall" class="waterfall-layout">
      <WaterfallSection
        :waterfall-goods-list="goodsList"
        :waterfall-loading="loading"
        :waterfall-finished="finished"
        :waterfall-button-can-show="false"
        :waterfall-render-complete="true"
        :skeleton-states="{ waterfall: false }"
        load-mode="scroll"
        @load-more="handleWaterfallLoadMore"
      >
        <template #item="{ item }">
          <GoodsWaterfallItem
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </template>
        <template #empty>
          <section v-if="goodsList.length <= 0 && !isLoading && !isWaterfall" class="empty-state">
            <WoEmpty :description="emptyDescription" />
          </section>
        </template>
      </WaterfallSection>
    </section>

    <section v-if="goodsList.length <= 0 && !isLoading" class="empty-state">
      <WoEmpty :description="emptyDescription" />
    </section>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import GoodsSkeletonLoader from './GoodsSkeletonLoader.vue'
import ProductListItem from './ProductListItem.vue'
import GoodsWaterfallItem from './GoodsWaterfallItem.vue'
import WaterfallSection from './WaterfallSection.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  finished: {
    type: Boolean,
    default: false
  },
  isWaterfall: {
    type: Boolean,
    default: false
  },
  breakpoints: {
    type: Object,
    default: () => getDefaultBreakpoints()
  },
  emptyDescription: {
    type: String,
    default: '暂无商品'
  }
})

const {
  goodsList,
  isLoading,
  loading,
  finished,
  isWaterfall,
  breakpoints,
  emptyDescription
} = toRefs(props)

const emit = defineEmits(['load-more', 'item-click', 'add-cart', 'update:loading'])

const handleLoadMore = () => {
  emit('load-more')
}

const handleUpdateLoading = (value) => {
  emit('update:loading', value)
}

// 瀑布流滚动触底时触发，模拟 van-list 的 update:loading 以兼容父组件
const handleWaterfallLoadMore = () => {
  emit('update:loading', true)
  emit('load-more')
}

const handleItemClick = (item) => {
  emit('item-click', item)
}

const handleAddCart = (item) => {
  emit('add-cart', item)
}
</script>

<style scoped lang="less">
.goods-list-layout {
  .list-layout {
    margin-top: 2px;

    .goods-list-container {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .waterfall-layout {
    :deep(.home-waterfall-container) {
      padding: 0;
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
